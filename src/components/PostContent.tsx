'use client';

import { useEffect, useRef } from 'react';
import Prism from 'prismjs';

// Import các ngôn ngữ phổ biến
import "prismjs/components/prism-java";
import "prismjs/components/prism-javascript";
import "prismjs/components/prism-typescript";
import "prismjs/components/prism-python";
import "prismjs/components/prism-jsx";
import "prismjs/components/prism-tsx";
import "prismjs/components/prism-css";
import "prismjs/components/prism-scss";
import "prismjs/components/prism-json";
import "prismjs/components/prism-bash";
import "prismjs/components/prism-sql";
import "prismjs/components/prism-php";
import "prismjs/components/prism-c";
import "prismjs/components/prism-cpp";
import "prismjs/components/prism-csharp";
import "prismjs/components/prism-go";
import "prismjs/components/prism-rust";
import "prismjs/components/prism-kotlin";
import "prismjs/components/prism-swift";
import "prismjs/components/prism-dart";

interface PostContentProps {
  content: string;
}

export function PostContent({ content }: PostContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Highlight code blocks sau khi content được render
    const highlightCode = () => {
      if (typeof Prism !== 'undefined' && contentRef.current) {
        // Tìm tất cả code blocks trong content
        const codeBlocks = contentRef.current.querySelectorAll('pre[class*="language-"]');

        if (codeBlocks.length > 0) {
          // Highlight từng code block
          codeBlocks.forEach((block) => {
            const codeElement = block.querySelector('code');
            if (codeElement) {
              // Đảm bảo code element có class language
              const preClass = block.className;
              if (preClass && !codeElement.className.includes('language-')) {
                codeElement.className = preClass;
              }
              Prism.highlightElement(codeElement);
            } else {
              Prism.highlightElement(block);
            }
          });
        } else {
          // Fallback: tìm tất cả pre và code elements
          const allCodeBlocks = contentRef.current.querySelectorAll('pre, code[class*="language-"]');
          allCodeBlocks.forEach((block) => {
            if (block.tagName === 'PRE' && !block.className.includes('language-')) {
              // Nếu pre không có class language, thêm class mặc định
              block.className += ' language-none';
            }
            Prism.highlightElement(block);
          });
        }
      }
    };

    // Delay một chút để đảm bảo DOM đã được render
    const timer = setTimeout(highlightCode, 100);

    return () => clearTimeout(timer);
  }, [content]);

  return (
    <div className="prose dark:prose-invert max-w-none" ref={contentRef}>
      <div dangerouslySetInnerHTML={{ __html: content }} />
    </div>
  );
}