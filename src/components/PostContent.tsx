'use client';

import { useEffect, useRef } from 'react';

interface PostContentProps {
  content: string;
}

export function PostContent({ content }: PostContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const highlightCode = async () => {
      if (!contentRef.current || typeof window === 'undefined') return;

      try {
        console.log('🔍 Starting code highlighting...');

        // Dynamic import Prism
        const { default: Prism } = await import('prismjs');
        console.log('✅ Prism loaded:', !!Prism);

        // Import các ngôn ngữ cần thiết
        await Promise.all([
          import('prismjs/components/prism-java' as any),
          import('prismjs/components/prism-javascript' as any),
          import('prismjs/components/prism-typescript' as any),
          import('prismjs/components/prism-python' as any),
          import('prismjs/components/prism-jsx' as any),
          import('prismjs/components/prism-css' as any),
          import('prismjs/components/prism-json' as any),
          import('prismjs/components/prism-bash' as any),
          import('prismjs/components/prism-sql' as any),
        ]);
        console.log('✅ Language components loaded');

        // Tìm tất cả code blocks
        const codeBlocks = contentRef.current.querySelectorAll('pre[class*="language-"]');
        console.log('🔍 Found code blocks:', codeBlocks.length);

        if (codeBlocks.length > 0) {
          codeBlocks.forEach((block, index) => {
            console.log(`🔍 Processing block ${index}:`, block.className);
            const codeElement = block.querySelector('code');
            if (codeElement) {
              console.log(`  - Code element class:`, codeElement.className);
              // Copy class từ pre sang code nếu cần
              if (!codeElement.className && block.className) {
                codeElement.className = block.className;
                console.log(`  - Updated code class to:`, codeElement.className);
              }
              Prism.highlightElement(codeElement);
              console.log(`  ✅ Highlighted block ${index}`);
            }
          });
        } else {
          console.log('🔍 No language-specific blocks found, trying fallback...');
          // Fallback: highlight tất cả
          Prism.highlightAllUnder(contentRef.current);
          console.log('✅ Fallback highlighting completed');
        }
      } catch (error) {
        console.error('❌ Error highlighting code:', error);
      }
    };

    // Delay để đảm bảo DOM ready
    const timer = setTimeout(highlightCode, 200);
    return () => clearTimeout(timer);
  }, [content]);

  return (
    <div className="prose dark:prose-invert max-w-none" ref={contentRef}>
      <div dangerouslySetInnerHTML={{ __html: content }} />
    </div>
  );
}